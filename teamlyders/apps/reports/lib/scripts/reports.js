TL.Ready(["Inputs", "Downloader"], () => {
	TL.AddScript("resource?src=scripts/popups.js", function () {
		TL.ImportTemplate("resource?src=templates/reports.html", () => {
			let Reports = {};
			let Report = "";
			let Category = "";

			// Collect the get params for a requested report
			let GetParams = TL.Location.URLParams();

			/*
			 **
			 **
			 **
			 **
			 **
			 ** Load the aside report categories
			 ** ===============================
			 */
			const LoadCatagories = () => {
				// Load the aside category template
				let Template = TL.Template("Report-Category");

				// Iterate through the data to populate the template
				if (Object.keys(Reports).length > 0) {
					for (const Category of Object.keys(Reports)) {
						// Count the number of reports in each category
						let ReportCount = Object.keys(Reports[Category]).length;

						// Destructure data
						let Data = { Category: Category, ReportCount: ReportCount };

						// Fill template
						let FilledTemplate = TL.FillTemplate(Template, Data);

						// Append the template
						$("section.report-category").append(FilledTemplate);
					}
				}

				return true;
			};
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Load the selected category's reports
			 ** ===============================
			 */
			const LoadReports = () => {
				// Get the report template
				let Template = TL.Template("Report");

				// Clear the desktop report section
				$("section.reports > .report-selector").hide().empty();

				// Show the select message
				$("section.message").show();

				// Append the reports to the category
				for (const Report in Reports?.[Category]) {
					// Fill template
					let FilledTemplate = TL.FillTemplate(Template, Reports?.[Category]?.[Report]);

					// Append to desktop
					$("section.reports > .report-selector").append(FilledTemplate);
				}

				// Hide the reports message
				$("section.message").hide();

				// Display the reports
				$("section.reports > .report-selector").css("display", "block");

				// Is category on mobile not expanded?
				if ($(`.category-container[data-category='${Category}'] > .mobile-container > .mobile-view`).children().length <= 0) {
					// Create the report buttons and append
					for (const Report in Reports?.[Category]) {
						// Fill template
						let FilledTemplate = TL.FillTemplate(Template, Reports?.[Category]?.[Report]);

						// Append to mobile
						$(`.category-container[data-category='${Category}'] > .mobile-container > .mobile-view`).append(FilledTemplate);
					}
					// Show report buttons
					$(`.category-container[data-category='${Category}'] > .mobile-container > .mobile-view`).slideToggle("fast", "swing");

					// Animate + to x on mobile selector
					$(`.category-container[data-category='${Category}'] > .toggle-indicator > img`).addClass("expanded");

					//We need to collapse mobile and empty the div
				} else {
					$(`.category-container[data-category='${Category}'] > .mobile-container > .mobile-view`).slideToggle("fast", "swing", () => {
						$(`.category-container[data-category='${Category}'] > .mobile-container > .mobile-view`).empty();
					});

					// Animate x to + on mobile selector
					$(`.category-container[data-category='${Category}'] > .toggle-indicator > img`).removeClass("expanded");
					$(`.category-container[data-category='${Category}']`).removeClass("mobile-selected");
				}
				return true;
			};

			/*
			 **
			 **
			 **
			 **
			 **
			 ** Export Report
			 ** ===============================
			 */
			const Export = async (Data) => {
				// We call the Downloader to export the report with the correct file type and display name.
				// We also pass the configuration name to the Downloader so it can use the correct style sheet.
				return await TL.Downloader.Export({
					Data,
					FileType: Reports?.[Category]?.[Report]?.Format,
					DisplayName: Reports?.[Category]?.[Report]?.["Display Name"],
					ConfigurationName: Reports?.[Category]?.[Report].Report,
				});
			};

			/*
			 **
			 **
			 **
			 **
			 **
			 ** Listener to select report categories
			 ** ===============================
			 */
			$(document).on("click", ".grid > section.report-category > .category-container", function (e) {
				// Remove selected from all aside categories
				$(".grid >section.report-category > .category-container").each(function () {
					$(this).removeClass("selected");
				});

				// Add selected to aside and mobile
				$(this).addClass("selected").addClass("mobile-selected");

				// Remove expanded from aside
				$(".report-category").removeClass("expanded");

				// Get the category
				Category = $(this).data("category");

				// Start loading
				TL.Loading.Start("wrapper");

				// Load the reports
				LoadReports(Category);

				// Stoop loading
				TL.Loading.Stop(".wrapper");
			});
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Listener to go back an forth categories and reports
			 ** ===============================
			 */
			$(document).on("click", "button.toggle-aside-previews", function (e) {
				$(".report-category").toggleClass("expanded");
			});

			/*
			 **
			 **
			 **
			 **
			 **
			 ** Listener to select a report
			 ** ===============================
			 */

			$(document).on("click", ".report-container > .TL-Button.report", function (e) {
				// Stop the event from bubbling up
				e.stopPropagation();

				if ($(".report-container .TL-Loading").length > 0) {
					TL.Notify.Banner("Please wait", "Please wait for the current report to finish downloading");
					return false;
				}

				// Get the report
				Report = $(this).data("name");

				// Verify the category is set..exit if cannot find the category
				Category = GetReportCategory(Report);
				if (!Category) {
					TL.Notify.Banner("Oops!", `${Report} is not a valid report name`);
					return;
				}

				// Get the popup type
				let Popup = Reports?.[Category]?.[Report]?.["Popup"];

				// Show popup if it exists
				if (Popup) {
					// Fire the popup
					PopupMap[Popup](Report, Export);
					return;
				}

				// Start loading
				TL.Loading.Start('.report-container[data-report="' + Report + '"]');

				// Agent to get the report
				TL.Agent({
					agent: ["app", "get-report"],
					data: { Report },
					async success(Result) {
						// Start the loading here because the retrieve-download-data agent is about to be called.
						TL.Loading.Start('.report-container[data-report="' + Report + '"]');
						// Export the report
						await Export(Result);
						// Stop loading
						TL.Loading.Stop('.report-container[data-report="' + Report + '"]');
					},
					//Agent returned an error, pass it to the user
					error(error) {
						// Stop loading
						TL.Loading.Stop('.report-container[data-report="' + Report + '"]');

						TL.Notify.Banner("Oops!", error);
					},
				});
			});

			/*
			 **
			 **
			 **
			 **
			 **
			 ** Download the report.  Report object must contain report name and any other required params
			 ** ===============================
			 */
			const GetReport = (Report) => {
				TL.Loading.Start(".wrapper");
				TL.Agent({
					agent: ["app", "get-report"],
					data: Report,
					async success(Result) {
						// Export the report
						await Export(Result);

						// Stop loading
						TL.Loading.Stop(".wrapper");
					},
					//Agent returned an error, pass it to the user
					error(error) {
						// Stop loading
						TL.Loading.Stop(".wrapper");

						TL.Notify.Banner("Oops!", error);
					},
				});
			};
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Function to extract the category report title
			 ** ===============================
			 */
			const GetReportCategory = (RequestedReport) => {
				// Loop through the reports
				for (const ReportCategory in Reports) {
					// Return the category if the report is in the category object
					if (RequestedReport in Reports[ReportCategory]) {
						return ReportCategory;
					}
				}
				// Category not found
				return false;
			};
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Formats the get params to send to the back end. Need to capitalize the Request Keys, report name, and remove underscores
			 ** ===============================
			 */
			const FormatGetParams = (GetParams) => {
				// Return false if no report param exists
				if (!GetParams?.["report"]) {
					return false;
				}

				// Need to capitalize the words in the report name and remove the underscore between words
				let ReportName = GetParams["report"]
					.split("_")
					.map((s) => s.charAt(0).toUpperCase() + s.slice(1))
					.join(" ");

				// Replace the report name with the formatted string
				GetParams["report"] = ReportName;

				let Request = {};

				// Loop through the params and capitalize the key to send back the request object
				for (const Key in GetParams) {
					Request[Key.charAt(0).toUpperCase() + Key.slice(1)] = GetParams[Key];
				}
				// Return the formatted request
				return Request;
			};
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Start Loading..Initial load of page
			 ** ===============================
			 */
			TL.Loading.Start(".wrapper");
			/*
			 **
			 **
			 **
			 **
			 **
			 ** Initial agent call to get all reports
			 ** ===============================
			 */
			TL.Agent({
				agent: ["app", "get-reports"],
				data: {},
				success(Result) {
					// Save result in global reports variable
					Reports = Result;

					// Load the report aside categories
					LoadCatagories();

					// Auto click on first report
					$(".category-container")[0].click();

					TL.Loading.Stop(".wrapper");

					// We have a requested report from a get param
					if (GetParams?.["report"]) {
						// Format the get param keys... capitalize and remove underscores
						GetParams = FormatGetParams(GetParams);

						//   Extract the report name
						const ReportName = GetParams?.["Report"];

						// Get the report category, this is needed to find the correct output format
						let ReportCategory = GetReportCategory(ReportName);

						// Get the report!
						if (ReportCategory) {
							// Update the report category and report name
							Category = ReportCategory;
							Report = ReportName;

							GetReport(GetParams);

							// Else no category was found, not a valid report name
						} else {
							TL.Notify.Banner("Oops!", `${ReportName} is not a valid report name`);
						}
					}
				},
				//Agent returned an error, pass it to the user
				error(error) {
					// Hide the wrapper and display the zero results message
					$(".wrapper").hide();
					$("section.zero-results").show();

					// Stop loading
					TL.Loading.Stop(".wrapper");

					// Display error message
					TL.Notify.Banner("Oops!", error);
				},
			});
		});
	});
});
