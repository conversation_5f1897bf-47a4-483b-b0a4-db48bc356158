<!-- Import shared and custom CSS -->
<link rel="stylesheet" type="text/css" href="resource?src=styles/index.css">
<script src="resource?src=scripts/reporting.js"></script>

<div class="reporting-app" id="reportingApp">
    <!-- Mobile Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle" aria-expanded="false" aria-label="Toggle sidebar">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" aria-hidden="true"></div>

    <aside class="sidebar">
        <div class="sidebar-header">
            <h1>Reports</h1>
            <button class="sidebar-close" id="sidebarClose" aria-label="Close sidebar">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        
        <!-- Favorites Section -->
        <div class="favorites-section">
            <ul class="favorites-list" id="favoritesList">
                <li class="category-item" data-category="favorites">
                    <div class="category-name">Favorites</div>
                    <span class="category-count" id="favoritesCount">0 Reports</span>
                </li>
            </ul>
        </div>
        
        <!-- Category Separator -->
        <div class="category-separator"></div>
        
        <!-- Main Categories Section -->
        <div class="categories-section">
            <ul class="category-list" id="categoryList">
                <li class="category-item active" data-category="maintenance">
                    <div class="category-name">Maintenance</div>
                    <span class="category-count">2 Reports</span>
                </li>
                <li class="category-item" data-category="animals">
                    <div class="category-name">Animals</div>
                    <span class="category-count">1 Reports</span>
                </li>
                <li class="category-item" data-category="admin">
                    <div class="category-name">Admin</div>
                    <span class="category-count">4 Reports</span>
                </li>
                <li class="category-item" data-category="employees">
                    <div class="category-name">Employees</div>
                    <span class="category-count">10 Reports</span>
                </li>
                <li class="category-item" data-category="labor">
                    <div class="category-name">Labor</div>
                    <span class="category-count">3 Reports</span>
                </li>
                <li class="category-item" data-category="financial">
                    <div class="category-name">Financial</div>
                    <span class="category-count">11 Reports</span>
                </li>
                <li class="category-item" data-category="training">
                    <div class="category-name">Training</div>
                    <span class="category-count">1 Reports</span>
                </li>
                <li class="category-item" data-category="turnover">
                    <div class="category-name">Turnover</div>
                    <span class="category-count">2 Reports</span>
                </li>
                <li class="category-item" data-category="delivery">
                    <div class="category-name">Delivery</div>
                    <span class="category-count">6 Reports</span>
                </li>
                <li class="category-item" data-category="stores">
                    <div class="category-name">Stores</div>
                    <span class="category-count">6 Reports</span>
                </li>
                <li class="category-item" data-category="vacation">
                    <div class="category-name">Vacation</div>
                    <span class="category-count">1 Reports</span>
                </li>
            </ul>
        </div>
    </aside>
    
    <main class="main-content">
        <div class="content-header">
            <h2 id="categoryTitle">Maintenance</h2>
            <p id="categoryDescription">System maintenance and operational reports</p>
        </div>
        
        <div class="reports-grid" id="reportsGrid">
            <!-- Reports will be populated by JavaScript -->
        </div>
        
        <div class="empty-state" id="emptyState" style="display: none;">
            <h3>No reports available</h3>
            <p>There are no reports in this category yet.</p>
        </div>
    </main>
</div>
