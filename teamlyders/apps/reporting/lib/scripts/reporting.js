TL.Ready([], function () {
	TL.AddScript("resource?src=scripts/reporting-shared.js", function () {
		// Define global state
		const State = {
			CurrentCategory: "maintenance",
			ReportData: {},
			FavoriteReports: [],
		};

		// Navigation Object To Handle URL And Routing
		const Navigation = {
			GetCategoryFromURL: () => {
				const Category = TL.Location.URLParams("category");
				return Category && State.ReportData[Category] ? Category : "maintenance";
			},
			UpdateURL: (CategoryKey) => {
				if (!CategoryKey) return;
				TL.Browser.EditURL(`reporting?category=${CategoryKey}`);
			},
		};

		// Report Data Organized By Category
		const ReportData = {
			favorites: {
				Title: "Favorites",
				Description: "Your favorite reports for quick access",
				Reports: [],
			},
			maintenance: {
				Title: "Maintenance",
				Description: "System maintenance and operational reports",
				Reports: [
					{
						Title: "System Health Check",
						Description: "Monitor overall system performance and identify potential issues",
						Icon: "🔧",
					},
					{
						Title: "Equipment Status Report",
						Description: "Current status and maintenance schedules for all equipment",
						Icon: "⚙️",
					},
				],
			},
			animals: {
				Title: "Animals",
				Description: "Animal care and management reports",
				Reports: [
					{
						Title: "Animal Wellness Report",
						Description: "Health status and care records for all animals in the system",
						Icon: "🐾",
					},
				],
			},
			admin: {
				Title: "Admin",
				Description: "Administrative and management reports",
				Reports: [
					{
						Title: "User Access Log",
						Description: "Track user login activities and system access patterns",
						Icon: "👤",
					},
					{
						Title: "System Configuration",
						Description: "Current system settings and configuration status",
						Icon: "⚙️",
					},
					{
						Title: "Backup Status Report",
						Description: "Status of automated backups and data integrity checks",
						Icon: "💾",
					},
					{
						Title: "Security Audit Report",
						Description: "Security incidents and compliance monitoring",
						Icon: "🔒",
					},
				],
			},
			employees: {
				Title: "Employees",
				Description: "Employee management and HR reports",
				Reports: [
					{
						Title: "Avibra Active Employees",
						Description: "This report lists all the active employees in a format ready to send to Avibra",
						Icon: "👥",
					},
					{
						Title: "Employee Retention",
						Description: "No description available",
						Icon: "📊",
					},
					{
						Title: "Employee Roster",
						Description: "No description available",
						Icon: "👥",
					},
					{
						Title: "Employee Roster With Pay Rate",
						Description: "No description available",
						Icon: "👥",
					},
					{
						Title: "Ghost Employees",
						Description: "This report lists active employees that have been terminated or employees who have not yet completed onboarding",
						Icon: "👻",
					},
					{
						Title: "GRO Bonus Retention",
						Description: "This report shows all employees who have achieved the GRO level",
						Icon: "🏆",
					},
					{
						Title: "Historical Promotions",
						Description: "No description available",
						Icon: "📈",
					},
					{
						Title: "Incoming New Hires",
						Description: "This report shows all incoming new hires that need to be integrated into the payroll system",
						Icon: "🆕",
					},
					{
						Title: "Job Code Mismatch",
						Description: "This report provides managers with the ability to identify mismatches between the Clock System",
						Icon: "⚠️",
					},
					{
						Title: "Promotion Retention",
						Description: "No description available",
						Icon: "📊",
					},
				],
			},
			labor: {
				Title: "Labor",
				Description: "Labor management and scheduling reports",
				Reports: [
					{
						Title: "Labor Hours Summary",
						Description: "Overview of total labor hours across all departments",
						Icon: "⏰",
					},
					{
						Title: "Overtime Analysis",
						Description: "Track overtime hours and costs by department and employee",
						Icon: "📊",
					},
					{
						Title: "Scheduling Compliance",
						Description: "Monitor adherence to scheduled shifts and labor law compliance",
						Icon: "📅",
					},
				],
			},
			financial: {
				Title: "Financial",
				Description: "Financial performance and accounting reports",
				Reports: [
					{
						Title: "Revenue Report",
						Description: "Monthly revenue breakdown by department and location",
						Icon: "💰",
					},
					{
						Title: "Expense Analysis",
						Description: "Detailed analysis of operational expenses and cost centers",
						Icon: "📊",
					},
					{
						Title: "Profit & Loss Statement",
						Description: "Comprehensive P&L statement with year-over-year comparisons",
						Icon: "📈",
					},
					{
						Title: "Budget Variance Report",
						Description: "Compare actual performance against budgeted amounts",
						Icon: "📋",
					},
					{
						Title: "Cash Flow Analysis",
						Description: "Track cash inflows and outflows across all operations",
						Icon: "💵",
					},
					{
						Title: "Cost Per Unit Analysis",
						Description: "Calculate cost efficiency metrics by product or service",
						Icon: "📊",
					},
					{
						Title: "Tax Preparation Report",
						Description: "Generate reports for quarterly and annual tax filings",
						Icon: "📄",
					},
					{
						Title: "Accounts Receivable",
						Description: "Outstanding invoices and payment collection status",
						Icon: "💳",
					},
					{
						Title: "Accounts Payable",
						Description: "Vendor payments and outstanding obligations summary",
						Icon: "🧾",
					},
					{
						Title: "Financial Dashboard",
						Description: "Real-time financial KPIs and performance indicators",
						Icon: "📊",
					},
					{
						Title: "Investment Performance",
						Description: "Track performance of business investments and assets",
						Icon: "📈",
					},
					{
						Title: "Monthly Cash Flow Forecast",
						Description: "Projected cash inflows and outflows for the coming months",
						Icon: "🔮",
					},
					{
						Title: "Daily Sales By Hour",
						Description: "Breakdown of daily sales performance by hour",
						Icon: "🕒",
					},
					{
						Title: "Gross Margin Analysis",
						Description: "Assess gross margins across products and categories",
						Icon: "📈",
					},
					{
						Title: "Net Profit Trend",
						Description: "Historical net profit trend with moving averages",
						Icon: "📉",
					},
					{
						Title: "Cost Center Spend",
						Description: "Monthly spending by cost center",
						Icon: "🏷️",
					},
					{
						Title: "Vendor Spend Analysis",
						Description: "Top vendors by spend and contract compliance",
						Icon: "🤝",
					},
					{
						Title: "Product Profitability",
						Description: "Profitability metrics for each product line",
						Icon: "🧾",
					},
					{
						Title: "Break-Even Analysis",
						Description: "Calculate break-even points by product and store",
						Icon: "⚖️",
					},
					{
						Title: "Revenue By Channel",
						Description: "Compare revenue across online, in-store, and wholesale channels",
						Icon: "🛒",
					},
					{
						Title: "Expense Forecast",
						Description: "Forecasted expenses for the upcoming quarter",
						Icon: "🔭",
					},
					{
						Title: "Capital Expenditure Plan",
						Description: "Planned capital investments and depreciation schedules",
						Icon: "🏗️",
					},
					{
						Title: "Cash Conversion Cycle",
						Description: "Measure of how quickly the company converts investments into cash",
						Icon: "🔁",
					},
					{
						Title: "Aging Receivables",
						Description: "Aging buckets for outstanding customer invoices",
						Icon: "⏳",
					},
					{
						Title: "Aging Payables",
						Description: "Outstanding vendor invoices by age bucket",
						Icon: "⌛",
					},
					{
						Title: "Variance To Forecast",
						Description: "Actual results vs forecasted amounts with drilldowns",
						Icon: "📊",
					},
					{
						Title: "Fixed vs Variable Cost Analysis",
						Description: "Breakdown of fixed and variable costs for planning",
						Icon: "🔧",
					},
					{
						Title: "Scenario Planning (What-If)",
						Description: "Run multiple financial scenarios to measure impact",
						Icon: "🧮",
					},
					{
						Title: "Profitability By Location",
						Description: "Location-level profit and loss comparisons",
						Icon: "📍",
					},
					{
						Title: "Customer Lifetime Value",
						Description: "Estimate CLTV across customer segments",
						Icon: "💎",
					},
					{
						Title: "Churn Revenue Impact",
						Description: "Revenue lost due to customer churn",
						Icon: "📉",
					},
					{
						Title: "Subscription Revenue Cohorts",
						Description: "Cohort analysis for recurring revenue streams",
						Icon: "🧩",
					},
					{
						Title: "Deferred Revenue Schedule",
						Description: "Track deferred revenue and recognition timelines",
						Icon: "📆",
					},
					{
						Title: "Financial Ratios Overview",
						Description: "Key financial ratios (ROE, ROI, Current, Quick) at a glance",
						Icon: "📐",
					},
					{
						Title: "EKPI — Executive KPIs",
						Description: "High-level executive financial KPIs for board reporting",
						Icon: "📈",
					},
				],
			},
			training: {
				Title: "Training",
				Description: "Employee training and development reports",
				Reports: [
					{
						Title: "Training Completion Report",
						Description: "Track employee training progress and completion rates",
						Icon: "🎓",
					},
				],
			},
			turnover: {
				Title: "Turnover",
				Description: "Employee turnover and retention analysis",
				Reports: [
					{
						Title: "Turnover Rate Analysis",
						Description: "Calculate turnover rates by department and time period",
						Icon: "🔄",
					},
					{
						Title: "Exit Interview Summary",
						Description: "Compile feedback from departing employees",
						Icon: "📝",
					},
				],
			},
			delivery: {
				Title: "Delivery",
				Description: "Delivery operations and logistics reports",
				Reports: [
					{
						Title: "Delivery Performance",
						Description: "Track on-time delivery rates and customer satisfaction",
						Icon: "🚚",
					},
					{
						Title: "Route Optimization",
						Description: "Analyze delivery routes for efficiency improvements",
						Icon: "🗺️",
					},
					{
						Title: "Driver Performance",
						Description: "Monitor individual driver metrics and performance",
						Icon: "👨‍💼",
					},
					{
						Title: "Fuel Cost Analysis",
						Description: "Track fuel consumption and costs across fleet",
						Icon: "⛽",
					},
					{
						Title: "Vehicle Maintenance Log",
						Description: "Schedule and track vehicle maintenance activities",
						Icon: "🔧",
					},
					{
						Title: "Customer Delivery Feedback",
						Description: "Compile customer ratings and delivery experience feedback",
						Icon: "⭐",
					},
				],
			},
			stores: {
				Title: "Stores",
				Description: "Store operations and performance reports",
				Reports: [
					{
						Title: "Store Performance Dashboard",
						Description: "Compare performance metrics across all store locations",
						Icon: "🏪",
					},
					{
						Title: "Inventory Levels Report",
						Description: "Current inventory status and reorder recommendations",
						Icon: "📦",
					},
					{
						Title: "Sales by Location",
						Description: "Revenue and sales volume breakdown by store",
						Icon: "💰",
					},
					{
						Title: "Customer Traffic Analysis",
						Description: "Track foot traffic patterns and peak hours",
						Icon: "👥",
					},
					{
						Title: "Store Compliance Audit",
						Description: "Monitor adherence to company standards and policies",
						Icon: "✅",
					},
					{
						Title: "Staff Scheduling Report",
						Description: "Optimize staff scheduling based on traffic patterns",
						Icon: "📅",
					},
					{
						Title: "Stores List",
						Description: "List of active stores",
						Icon: "📅",
					},
					{
						Title: "Store Contacts",
						Description: "Stores contact list",
						Icon: "📅",
					},
					{
						Title: "Showstoppers",
						Description: "Stores showstopper status",
						Icon: "📅",
					},
					{
						Title: "Avibra Active Employees",
						Description: "Avibra Active Employees",
						Icon: "📅",
					},
				],
			},
			vacation: {
				Title: "Vacation",
				Description: "Employee vacation and time-off management",
				Reports: [
					{
						Title: "Vacation Balance Report",
						Description: "Track available vacation days and usage by employee",
						Icon: "🏖️",
					},
				],
			},
		};

		// Store Report Data In State
		State.ReportData = ReportData;

		// DOM Elements
		const Elements = {
			FavoritesList: document.getElementById("favoritesList"),
			FavoritesCount: document.getElementById("favoritesCount"),
			CategoryList: document.getElementById("categoryList"),
			CategoryTitle: document.getElementById("categoryTitle"),
			CategoryDescription: document.getElementById("categoryDescription"),
			ReportsGrid: document.getElementById("reportsGrid"),
			EmptyState: document.getElementById("emptyState"),
		};

		// Initialize The Reporting App
		InitializeReporting();

		/*
		 **
		 ** Initialize Reporting App
		 ** ========================
		 */
		function InitializeReporting() {
			GetReports();
			// Wait for templates to be available before proceeding
			WaitForTemplates(() => {
				const InitialCategory = Navigation.GetCategoryFromURL();
				State.CurrentCategory = InitialCategory;

				// Load Favorites From localStorage
				LoadFavoritesFromStorage();

				// Update Favorites Data And UI
				UpdateFavoritesData();
				UpdateFavoritesCount();

				// Set Up Initial State
				SetActiveCategory(InitialCategory, true); // Skip URL Update On First Load
				LoadCategory(InitialCategory);
				InitializeEventListeners();
			});
		}

		/*
		 **
		 ** Wait For Templates To Be Available
		 ** =================================
		 */
		function WaitForTemplates(Callback) {
			// Check if Report-Card template is available
			const CheckTemplates = () => {
				const Template = TL.Template("Report-Card");
				if (Template) {
					// Templates are ready, proceed with initialization
					Callback();
				} else {
					// Templates not ready yet, check again in 50ms
					setTimeout(CheckTemplates, 50);
				}
			};
			CheckTemplates();
		}

		/*
		 **
		 ** Initialize Event Listeners
		 ** ==========================
		 */
		function InitializeEventListeners() {
			// Setup Favorites List Event Listeners
			if (Elements.FavoritesList) {
				const FavoritesItems = Elements.FavoritesList.querySelectorAll(".category-item");
				FavoritesItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}

			// Setup Main Category List Event Listeners
			if (Elements.CategoryList) {
				const CategoryItems = Elements.CategoryList.querySelectorAll(".category-item");
				CategoryItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}

			// Initialize mobile sidebar behavior (toggle + auto-close on selection)
			InitializeMobileSidebar();
		}

		/*
		 **
		 ** Initialize Mobile Sidebar Toggle
		 ** =================================
		 */
		function InitializeMobileSidebar() {
			const Toggle = document.getElementById("sidebarToggle");
			const Close = document.getElementById("sidebarClose");
			const Sidebar = document.querySelector(".sidebar");
			const Backdrop = document.getElementById("sidebarBackdrop");
			const CategoryItems = Elements.CategoryList ? Elements.CategoryList.querySelectorAll(".category-item") : document.querySelectorAll(".category-item");

			if (!Toggle || !Sidebar || !Backdrop) return;

			const OpenSidebar = () => {
				Sidebar.classList.add("open");
				Backdrop.classList.add("show");
				Toggle.setAttribute("aria-expanded", "true");
				Backdrop.setAttribute("aria-hidden", "false");
			};

			const CloseSidebar = () => {
				Sidebar.classList.remove("open");
				Backdrop.classList.remove("show");
				Toggle.setAttribute("aria-expanded", "false");
				Backdrop.setAttribute("aria-hidden", "true");
			};

			Toggle.addEventListener("click", (e) => {
				e.stopPropagation();
				if (Sidebar.classList.contains("open")) CloseSidebar();
				else OpenSidebar();
			});

			// Close button event listener
			if (Close) {
				Close.addEventListener("click", (e) => {
					e.stopPropagation();
					CloseSidebar();
				});
			}

			Backdrop.addEventListener("click", CloseSidebar);

			// Close sidebar when a category is clicked (mobile)
			CategoryItems.forEach((Item) => {
				Item.addEventListener("click", () => {
					if (window.innerWidth <= 900) CloseSidebar();
				});
			});
		}

		/*
		 **
		 ** Handle Category Click
		 ** ====================
		 */
		function HandleCategoryClick(CategoryKey) {
			if (!CategoryKey || CategoryKey === State.CurrentCategory) return;

			State.CurrentCategory = CategoryKey;
			SetActiveCategory(CategoryKey);
			LoadCategory(CategoryKey);
		}

		/*
		 **
		 ** Set Active Category Visual State
		 ** ===============================
		 */
		function SetActiveCategory(CategoryKey, SkipUrl = false) {
			if (!CategoryKey) return;

			// Remove Active State From All Category Items In Both Lists
			const AllCategoryItems = [...(Elements.FavoritesList?.querySelectorAll(".category-item") || []), ...(Elements.CategoryList?.querySelectorAll(".category-item") || [])];

			// Remove Active From All Items First
			AllCategoryItems.forEach((Item) => {
				Item.classList.remove("active");
			});

			// Add Active State To The Matching Category
			const TargetItem = AllCategoryItems.find((Item) => Item.dataset.category === CategoryKey);
			if (TargetItem) {
				TargetItem.classList.add("active");
			}

			// Update URL If Not Skipped
			if (!SkipUrl) Navigation.UpdateURL(CategoryKey);
		}

		/*
		 **
		 ** Load Reports For Category
		 ** ========================
		 */
		function LoadCategory(CategoryKey) {
			TL.DebugLog("Loading Category:", CategoryKey);

			const Category = State.ReportData[CategoryKey];
			if (!Category) {
				ShowEmptyState();
				return;
			}

			// Update Page Header
			UpdateCategoryHeader(Category);

			// Clear And Populate Reports
			ClearReportsGrid();

			if (Category.Reports.length === 0) {
				ShowEmptyState();
				return;
			}

			RenderReports(Category.Reports, CategoryKey);
		}

		/*
		 **
		 ** Update Category Header
		 ** =====================
		 */
		function UpdateCategoryHeader(Category) {
			if (Elements.CategoryTitle) {
				Elements.CategoryTitle.textContent = Category.Title;
			}
			if (Elements.CategoryDescription) {
				Elements.CategoryDescription.textContent = Category.Description;
			}
		}

		/*
		 **
		 ** Clear Reports Grid
		 ** =================
		 */
		function ClearReportsGrid() {
			if (Elements.ReportsGrid) {
				Elements.ReportsGrid.innerHTML = "";
			}
		}

		/*
		 **
		 ** Render Reports
		 ** =============
		 */
		function RenderReports(Reports, CategoryKey) {
			// Hide Empty State And Show Reports Grid
			if (Elements.EmptyState) Elements.EmptyState.style.display = "none";
			if (Elements.ReportsGrid) Elements.ReportsGrid.style.display = "grid";

			// Create And Append Report Cards
			Reports.forEach((Report) => {
				const ReportCard = CreateReportCard(Report, CategoryKey);
				if (Elements.ReportsGrid && ReportCard) {
					Elements.ReportsGrid.appendChild(ReportCard);
				}
			});
		}

		/*
		 **
		 ** Create Report Card Element
		 ** =========================
		 */
		function CreateReportCard(Report, CategoryKey) {
			// Handle "No Description Available" Cases
			const DescriptionClass = Report.Description === "No description available" ? "no-description" : "";

			// Check If Report Is Favorited
			const ReportId = GetReportId(Report, CategoryKey);
			const IsFavorited = State.FavoriteReports.includes(ReportId);
			const FavoriteClass = IsFavorited ? "favorited" : "";
			const FavoriteTitle = IsFavorited ? "Remove from favorites" : "Add to favorites";

			// Prepare Template Data
			const TemplateData = {
				Category: CategoryKey,
				Icon: Report.Icon,
				Title: Report.Title,
				Description: Report.Description,
				DescriptionClass: DescriptionClass,
				FavoriteClass: FavoriteClass,
				FavoriteTitle: FavoriteTitle,
			};

			// Get Template And Fill With Data
			const Template = TL.Template("Report-Card");
			if (!Template) {
				TL.DebugLog("Template 'Report-Card' not found");
				return null;
			}

			const FilledTemplate = TL.FillTemplate(Template, TemplateData);
			if (!FilledTemplate) {
				TL.DebugLog("Failed to fill template for report:", Report.Title);
				return null;
			}

			// Create DOM Element
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = FilledTemplate;
			const Card = TempDiv.firstElementChild;

			if (!Card) {
				TL.DebugLog("Failed to create card element for report:", Report.Title);
				return null;
			}

			// Add Event Listeners
			AttachReportCardEvents(Card, Report, CategoryKey);

			return Card;
		}

		/*
		 **
		 ** Attach Report Card Event Listeners
		 ** =================================
		 */
		function AttachReportCardEvents(Card, Report, CategoryKey) {
			if (!Card) {
				TL.DebugLog("Cannot attach events: card is null for report:", Report.Title);
				return;
			}

			// Generate Report Button
			const GenerateBtn = Card.querySelector(".generate-btn");
			if (GenerateBtn) {
				GenerateBtn.addEventListener("click", function () {
					GenerateReport(Report.Title);
				});
			}

			// Favorite Button
			const FavoriteBtn = Card.querySelector(".favorite-btn");
			if (FavoriteBtn) {
				FavoriteBtn.addEventListener("click", function () {
					ToggleFavorite(FavoriteBtn, Report, CategoryKey);
				});
			}
		}

		/*
		 **
		 ** Show Empty State
		 ** ===============
		 */
		function ShowEmptyState() {
			if (Elements.ReportsGrid) Elements.ReportsGrid.style.display = "none";
			if (Elements.EmptyState) Elements.EmptyState.style.display = "block";
		}

		/*
		 **
		 ** Generate Report
		 ** ==============
		 */
		function GenerateReport(ReportTitle) {
			RetrieveReport(ReportTitle);
			TL.DebugLog("Generate report requested:", ReportTitle);
			// alert(`Generating report: ${ReportTitle}`);
			// TODO: Implement Actual Report Generation Logic
		}

		/*
		 **
		 ** Toggle Favorite Status
		 ** =====================
		 */
		function ToggleFavorite(FavoriteBtn, Report, CategoryKey) {
			const ReportId = GetReportId(Report, CategoryKey);
			const IsFavorited = State.FavoriteReports.includes(ReportId);

			if (IsFavorited) {
				// Remove From Favorites
				State.FavoriteReports = State.FavoriteReports.filter((Id) => Id !== ReportId);
				FavoriteBtn.classList.remove("favorited");
				FavoriteBtn.title = "Add to favorites";
				TL.DebugLog("Removed from favorites:", Report.Title);
			} else {
				// Add To Favorites
				State.FavoriteReports.push(ReportId);
				FavoriteBtn.classList.add("favorited");
				FavoriteBtn.title = "Remove from favorites";
				TL.DebugLog("Added to favorites:", Report.Title);
			}

			// Save To localStorage
			SaveFavoritesToStorage();

			// Update Favorites Data And Count
			UpdateFavoritesData();
			UpdateFavoritesCount();

			// If Currently Viewing Favorites, Refresh The View
			if (State.CurrentCategory === "favorites") {
				LoadCategory("favorites");
			}
		}

		/*
		 **
		 ** Get Report ID For Favorites Storage
		 ** ==================================
		 */
		function GetReportId(Report, CategoryKey) {
			return `${CategoryKey}:${Report.Title}`;
		}

		/*
		 **
		 ** Load Favorites From localStorage
		 ** ===============================
		 */
		function LoadFavoritesFromStorage() {
			try {
				const Stored = localStorage.getItem("reporting-favorites");
				if (Stored) {
					State.FavoriteReports = JSON.parse(Stored);
				}
			} catch (Error) {
				TL.DebugLog("Error loading favorites from storage:", Error);
				State.FavoriteReports = [];
			}
		}

		/*
		 **
		 ** Save Favorites To localStorage
		 ** =============================
		 */
		function SaveFavoritesToStorage() {
			try {
				localStorage.setItem("reporting-favorites", JSON.stringify(State.FavoriteReports));
			} catch (Error) {
				TL.DebugLog("Error saving favorites to storage:", Error);
			}
		}

		/*
		 **
		 ** Update Favorites Data
		 ** ====================
		 */
		function UpdateFavoritesData() {
			const FavoriteReports = [];

			// Loop Through All Categories And Collect Favorited Reports
			State.FavoriteReports.forEach((ReportId) => {
				const [CategoryKey, ReportTitle] = ReportId.split(":");
				const Category = State.ReportData[CategoryKey];

				if (Category) {
					const Report = Category.Reports.find((R) => R.Title === ReportTitle);
					if (Report) {
						FavoriteReports.push({
							...Report,
							OriginalCategory: CategoryKey,
						});
					}
				}
			});

			// Update The Favorites Category Data
			State.ReportData.favorites.Reports = FavoriteReports;
		}

		/*
		 **
		 ** Update Favorites Count Display
		 ** =============================
		 */
		function UpdateFavoritesCount() {
			if (Elements.FavoritesCount) {
				const Count = State.FavoriteReports.length;
				Elements.FavoritesCount.textContent = `${Count} Report${Count === 1 ? "" : "s"}`;
			}
		}

		/*
		 **
		 ** GetReport
		 ** ===============================
		 */

		/*
		 **
		 ** Get Reports
		 ** =====================
		 */
		function GetReports() {
			TL.Agent({
				agent: ["app", "get-user-reports"],
				data: {},
				success(Result) {
					TL.DebugLog("Fetched Reports:", Result);
				},
				error(error) {
					TL.Notify.Banner("Error", error);
				},
			});
		}

		/*
		 **
		 ** Retrieve the requested report
		 ** =============================
		 */
		function RetrieveReport(ReportTitle) {
			// Start loading
			TL.Loading.Start("main");
			TL.Agent({
				agent: ["app", "retrieve"],
				data: {
					ReportName: ReportTitle,
				},
				success(Result) {
					TL.Loading.Stop("main");
				},

				//Agent returned an error, pass it to the user
				error(error) {
					TL.Notify.Banner("Error", error);
					TL.Loading.Stop("main");
				},
			});
		}
	});
});
