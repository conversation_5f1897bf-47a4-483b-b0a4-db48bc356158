TL.Ready(["Downloader"], function () {
	TL.AddScript("resource?src=scripts/reporting-shared.js", function () {
		// Define global state
		const State = {
			CurrentCategory: null,
			ReportData: {},
			FavoriteReports: [],
			IsDataLoaded: false,
		};

		// Navigation Object To Handle URL And Routing
		const Navigation = {
			GetCategoryFromURL: () => {
				const Category = TL.Location.URLParams("category");
				return Category && State.ReportData[Category] ? Category : null;
			},
			UpdateURL: (CategoryKey) => {
				if (!CategoryKey) return;
				TL.Browser.EditURL(`reporting?category=${CategoryKey}`);
			},
			GetDefaultCategory: () => {
				// Return the first available category from the fetched data (excluding favorites)
				const Categories = Object.keys(State.ReportData).filter((cat) => cat !== "favorites");
				return Categories.length > 0 ? Categories[0] : "favorites";
			},
		};

		// DOM Elements
		const Elements = {
			FavoritesList: document.getElementById("favoritesList"),
			FavoritesCount: document.getElementById("favoritesCount"),
			CategoryList: document.getElementById("categoryList"),
			CategoryTitle: document.getElementById("categoryTitle"),
			CategoryDescription: document.getElementById("categoryDescription"),
			ReportsGrid: document.getElementById("reportsGrid"),
			EmptyState: document.getElementById("emptyState"),
		};

		// Initialize The Reporting App
		InitializeReporting();

		/*
		 **
		 ** Initialize Reporting App
		 ** ========================
		 */
		function InitializeReporting() {
			// Wait for templates to be available before proceeding
			WaitForTemplates(() => {
				// Get reports data first
				GetReports()
					.then(() => {
						// Determine initial category after data is loaded
						let InitialCategory = Navigation.GetCategoryFromURL();
						if (!InitialCategory || !State.ReportData[InitialCategory]) {
							InitialCategory = Navigation.GetDefaultCategory();
						}
						State.CurrentCategory = InitialCategory;

						// Load Favorites From localStorage
						LoadFavoritesFromStorage();

						// Update Favorites Data And UI
						UpdateFavoritesData();
						UpdateFavoritesCount();

						// Set Up Initial State
						SetActiveCategory(InitialCategory, true); // Skip URL Update On First Load
						LoadCategory(InitialCategory);
						InitializeEventListeners();
					})
					.catch((error) => {
						TL.DebugLog("Failed to initialize reporting:", error);
						// Show error state or fallback
						ShowEmptyState();
					});
			});
		}

		/*
		 **
		 ** Wait For Templates To Be Available
		 ** =================================
		 */
		function WaitForTemplates(Callback) {
			// Check if Report-Card template is available
			const CheckTemplates = () => {
				const Template = TL.Template("Report-Card");
				if (Template) {
					// Templates are ready, proceed with initialization
					Callback();
				} else {
					// Templates not ready yet, check again in 50ms
					setTimeout(CheckTemplates, 50);
				}
			};
			CheckTemplates();
		}

		/*
		 **
		 ** Initialize Event Listeners
		 ** ==========================
		 */
		function InitializeEventListeners() {
			// Setup Favorites List Event Listeners
			if (Elements.FavoritesList) {
				const FavoritesItems = Elements.FavoritesList.querySelectorAll(".category-item");
				FavoritesItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}

			// Setup Main Category List Event Listeners (for dynamically created categories)
			AttachCategoryEventListeners();

			// Initialize mobile sidebar behavior (toggle + auto-close on selection)
			InitializeMobileSidebar();
		}

		/*
		 **
		 ** Initialize Mobile Sidebar Toggle
		 ** =================================
		 */
		function InitializeMobileSidebar() {
			const Toggle = document.getElementById("sidebarToggle");
			const Close = document.getElementById("sidebarClose");
			const Sidebar = document.querySelector(".sidebar");
			const Backdrop = document.getElementById("sidebarBackdrop");
			const CategoryItems = Elements.CategoryList ? Elements.CategoryList.querySelectorAll(".category-item") : document.querySelectorAll(".category-item");

			if (!Toggle || !Sidebar || !Backdrop) return;

			const OpenSidebar = () => {
				Sidebar.classList.add("open");
				Backdrop.classList.add("show");
				Toggle.setAttribute("aria-expanded", "true");
				Backdrop.setAttribute("aria-hidden", "false");
			};

			const CloseSidebar = () => {
				Sidebar.classList.remove("open");
				Backdrop.classList.remove("show");
				Toggle.setAttribute("aria-expanded", "false");
				Backdrop.setAttribute("aria-hidden", "true");
			};

			Toggle.addEventListener("click", (e) => {
				e.stopPropagation();
				if (Sidebar.classList.contains("open")) CloseSidebar();
				else OpenSidebar();
			});

			// Close button event listener
			if (Close) {
				Close.addEventListener("click", (e) => {
					e.stopPropagation();
					CloseSidebar();
				});
			}

			Backdrop.addEventListener("click", CloseSidebar);

			// Close sidebar when a category is clicked (mobile)
			CategoryItems.forEach((Item) => {
				Item.addEventListener("click", () => {
					if (window.innerWidth <= 900) CloseSidebar();
				});
			});
		}

		/*
		 **
		 ** Process Reports Data From API
		 ** ============================
		 */
		function ProcessReportsData(ApiData) {
			TL.DebugLog("Processing API Data:", ApiData);

			// Initialize with favorites category
			const ProcessedData = {
				favorites: {
					Title: "Favorites",
					Description: "Your favorite reports for quick access",
					Reports: [],
				},
			};

			// Process each category from the API
			Object.keys(ApiData).forEach((CategoryKey) => {
				const CategoryReports = ApiData[CategoryKey];
				if (!Array.isArray(CategoryReports) || CategoryReports.length === 0) return;

				// Create category object
				const CategoryName = CategoryKey.charAt(0).toUpperCase() + CategoryKey.slice(1).toLowerCase();
				ProcessedData[CategoryKey.toLowerCase()] = {
					Title: CategoryName,
					Description: `${CategoryName} reports and analytics`,
					Reports: CategoryReports.map((Report) => ({
						Title: Report["Display Name"] || Report.Name,
						Description: Report.Description || "No description available",
						Icon: GetReportIcon(Report.Icon || "📊"),
						Name: Report.Name,
						Category: Report.Category,
						Format: Report.Format,
						Version: Report.Version,
					})),
				};
			});

			return ProcessedData;
		}

		/*
		 **
		 ** Get Report Icon
		 ** ==============
		 */
		function GetReportIcon(IconName) {
			const IconMap = {
				building: "🏢",
				alert: "⚠️",
				contact: "�",
				"people-group": "👥",
				default: "📊",
			};
			return IconMap[IconName] || IconMap.default;
		}

		/*
		 **
		 ** Render Category List
		 ** ===================
		 */
		function RenderCategoryList() {
			const CategoryList = Elements.CategoryList;
			if (!CategoryList) return;

			// Clear existing categories (except favorites which is handled separately)
			CategoryList.innerHTML = "";

			// Get categories excluding favorites
			const Categories = Object.keys(State.ReportData).filter((key) => key !== "favorites");

			// Render each category
			Categories.forEach((CategoryKey) => {
				const Category = State.ReportData[CategoryKey];
				const ReportCount = Category.Reports.length;

				const TemplateData = {
					Category: CategoryKey,
					Name: Category.Title,
					Count: ReportCount,
					ActiveClass: CategoryKey === State.CurrentCategory ? "active" : "",
				};

				const Template = TL.Template("Category-Item");
				if (Template) {
					const FilledTemplate = TL.FillTemplate(Template, TemplateData);
					const TempDiv = document.createElement("div");
					TempDiv.innerHTML = FilledTemplate;
					const CategoryItem = TempDiv.firstElementChild;

					if (CategoryItem) {
						CategoryList.appendChild(CategoryItem);
					}
				}
			});

			// Re-attach event listeners for new category items
			AttachCategoryEventListeners();
		}
		/*
		 **
		 ** Attach Category Event Listeners
		 ** ==============================
		 */
		function AttachCategoryEventListeners() {
			// Setup Main Category List Event Listeners
			if (Elements.CategoryList) {
				const CategoryItems = Elements.CategoryList.querySelectorAll(".category-item");
				CategoryItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}
		}

		/*
		 **
		 ** Handle Category Click
		 ** ====================
		 */
		function HandleCategoryClick(CategoryKey) {
			if (!CategoryKey || CategoryKey === State.CurrentCategory) return;

			State.CurrentCategory = CategoryKey;
			SetActiveCategory(CategoryKey);
			LoadCategory(CategoryKey);
		}

		/*
		 **
		 ** Set Active Category Visual State
		 ** ===============================
		 */
		function SetActiveCategory(CategoryKey, SkipUrl = false) {
			if (!CategoryKey) return;

			// Remove Active State From All Category Items In Both Lists
			const AllCategoryItems = [...(Elements.FavoritesList?.querySelectorAll(".category-item") || []), ...(Elements.CategoryList?.querySelectorAll(".category-item") || [])];

			// Remove Active From All Items First
			AllCategoryItems.forEach((Item) => {
				Item.classList.remove("active");
			});

			// Add Active State To The Matching Category
			const TargetItem = AllCategoryItems.find((Item) => Item.dataset.category === CategoryKey);
			if (TargetItem) {
				TargetItem.classList.add("active");
			}

			// Update URL If Not Skipped
			if (!SkipUrl) Navigation.UpdateURL(CategoryKey);
		}

		/*
		 **
		 ** Load Reports For Category
		 ** ========================
		 */
		function LoadCategory(CategoryKey) {
			TL.DebugLog("Loading Category:", CategoryKey);

			const Category = State.ReportData[CategoryKey];
			if (!Category) {
				ShowEmptyState();
				return;
			}

			// Update Page Header
			UpdateCategoryHeader(Category);

			// Clear And Populate Reports
			ClearReportsGrid();

			if (Category.Reports.length === 0) {
				ShowEmptyState();
				return;
			}

			RenderReports(Category.Reports, CategoryKey);
		}

		/*
		 **
		 ** Update Category Header
		 ** =====================
		 */
		function UpdateCategoryHeader(Category) {
			if (Elements.CategoryTitle) {
				Elements.CategoryTitle.textContent = Category.Title;
			}
			if (Elements.CategoryDescription) {
				Elements.CategoryDescription.textContent = Category.Description;
			}
		}

		/*
		 **
		 ** Clear Reports Grid
		 ** =================
		 */
		function ClearReportsGrid() {
			if (Elements.ReportsGrid) {
				Elements.ReportsGrid.innerHTML = "";
			}
		}

		/*
		 **
		 ** Render Reports
		 ** =============
		 */
		function RenderReports(Reports, CategoryKey) {
			// Hide Empty State And Show Reports Grid
			if (Elements.EmptyState) Elements.EmptyState.style.display = "none";
			if (Elements.ReportsGrid) Elements.ReportsGrid.style.display = "grid";

			// Create And Append Report Cards
			Reports.forEach((Report) => {
				const ReportCard = CreateReportCard(Report, CategoryKey);
				if (Elements.ReportsGrid && ReportCard) {
					Elements.ReportsGrid.appendChild(ReportCard);
				}
			});
		}

		/*
		 **
		 ** Create Report Card Element
		 ** =========================
		 */
		function CreateReportCard(Report, CategoryKey) {
			// Handle "No Description Available" Cases
			const DescriptionClass = Report.Description === "No description available" ? "no-description" : "";

			// Check If Report Is Favorited
			const ReportId = GetReportId(Report, CategoryKey);
			const IsFavorited = State.FavoriteReports.includes(ReportId);
			const FavoriteClass = IsFavorited ? "favorited" : "";
			const FavoriteTitle = IsFavorited ? "Remove from favorites" : "Add to favorites";

			// Prepare Template Data
			const TemplateData = {
				Category: CategoryKey,
				Icon: Report.Icon,
				Title: Report.Title,
				Description: Report.Description,
				DescriptionClass: DescriptionClass,
				FavoriteClass: FavoriteClass,
				FavoriteTitle: FavoriteTitle,
			};

			// Get Template And Fill With Data
			const Template = TL.Template("Report-Card");
			if (!Template) {
				TL.DebugLog("Template 'Report-Card' not found");
				return null;
			}

			const FilledTemplate = TL.FillTemplate(Template, TemplateData);
			if (!FilledTemplate) {
				TL.DebugLog("Failed to fill template for report:", Report.Title);
				return null;
			}

			// Create DOM Element
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = FilledTemplate;
			const Card = TempDiv.firstElementChild;

			if (!Card) {
				TL.DebugLog("Failed to create card element for report:", Report.Title);
				return null;
			}

			// Add Event Listeners
			AttachReportCardEvents(Card, Report, CategoryKey);

			return Card;
		}

		/*
		 **
		 ** Attach Report Card Event Listeners
		 ** =================================
		 */
		function AttachReportCardEvents(Card, Report, CategoryKey) {
			if (!Card) {
				TL.DebugLog("Cannot attach events: card is null for report:", Report.Title);
				return;
			}

			// Generate Report Button
			const GenerateBtn = Card.querySelector(".generate-btn");
			if (GenerateBtn) {
				GenerateBtn.addEventListener("click", function () {
					// Use the report Name (not Title) for the API call
					const ReportName = Report.Name || Report.Title;
					GenerateReport(ReportName);
				});
			}

			// Favorite Button
			const FavoriteBtn = Card.querySelector(".favorite-btn");
			if (FavoriteBtn) {
				FavoriteBtn.addEventListener("click", function () {
					ToggleFavorite(FavoriteBtn, Report, CategoryKey);
				});
			}
		}

		/*
		 **
		 ** Show Empty State
		 ** ===============
		 */
		function ShowEmptyState() {
			if (Elements.ReportsGrid) Elements.ReportsGrid.style.display = "none";
			if (Elements.EmptyState) Elements.EmptyState.style.display = "block";
		}

		/*
		 **
		 ** Generate Report
		 ** ==============
		 */
		function GenerateReport(ReportTitle) {
			RetrieveReport(ReportTitle);
			TL.DebugLog("Generate report requested:", ReportTitle);
			// alert(`Generating report: ${ReportTitle}`);
			// TODO: Implement Actual Report Generation Logic
		}

		/*
		 **
		 ** Toggle Favorite Status
		 ** =====================
		 */
		function ToggleFavorite(FavoriteBtn, Report, CategoryKey) {
			const ReportId = GetReportId(Report, CategoryKey);
			const IsFavorited = State.FavoriteReports.includes(ReportId);

			if (IsFavorited) {
				// Remove From Favorites
				State.FavoriteReports = State.FavoriteReports.filter((Id) => Id !== ReportId);
				FavoriteBtn.classList.remove("favorited");
				FavoriteBtn.title = "Add to favorites";
				TL.DebugLog("Removed from favorites:", Report.Title);
			} else {
				// Add To Favorites
				State.FavoriteReports.push(ReportId);
				FavoriteBtn.classList.add("favorited");
				FavoriteBtn.title = "Remove from favorites";
				TL.DebugLog("Added to favorites:", Report.Title);
			}

			// Save To localStorage
			SaveFavoritesToStorage();

			// Update Favorites Data And Count
			UpdateFavoritesData();
			UpdateFavoritesCount();

			// If Currently Viewing Favorites, Refresh The View
			if (State.CurrentCategory === "favorites") {
				LoadCategory("favorites");
			}
		}

		/*
		 **
		 ** Get Report ID For Favorites Storage
		 ** ==================================
		 */
		function GetReportId(Report, CategoryKey) {
			return `${CategoryKey}:${Report.Title}`;
		}

		/*
		 **
		 ** Load Favorites From localStorage
		 ** ===============================
		 */
		function LoadFavoritesFromStorage() {
			try {
				const Stored = localStorage.getItem("reporting-favorites");
				if (Stored) {
					State.FavoriteReports = JSON.parse(Stored);
				}
			} catch (Error) {
				TL.DebugLog("Error loading favorites from storage:", Error);
				State.FavoriteReports = [];
			}
		}

		/*
		 **
		 ** Save Favorites To localStorage
		 ** =============================
		 */
		function SaveFavoritesToStorage() {
			try {
				localStorage.setItem("reporting-favorites", JSON.stringify(State.FavoriteReports));
			} catch (Error) {
				TL.DebugLog("Error saving favorites to storage:", Error);
			}
		}

		/*
		 **
		 ** Update Favorites Data
		 ** ====================
		 */
		function UpdateFavoritesData() {
			const FavoriteReports = [];

			// Loop Through All Categories And Collect Favorited Reports
			State.FavoriteReports.forEach((ReportId) => {
				const [CategoryKey, ReportTitle] = ReportId.split(":");
				const Category = State.ReportData[CategoryKey];

				if (Category) {
					const Report = Category.Reports.find((R) => R.Title === ReportTitle);
					if (Report) {
						FavoriteReports.push({
							...Report,
							OriginalCategory: CategoryKey,
						});
					}
				}
			});

			// Update The Favorites Category Data
			State.ReportData.favorites.Reports = FavoriteReports;
		}

		/*
		 **
		 ** Update Favorites Count Display
		 ** =============================
		 */
		function UpdateFavoritesCount() {
			if (Elements.FavoritesCount) {
				const Count = State.FavoriteReports.length;
				Elements.FavoritesCount.textContent = `${Count} Report${Count === 1 ? "" : "s"}`;
			}
		}

		/*
		 **
		 ** Get Reports
		 ** =====================
		 */
		function GetReports() {
			return new Promise((resolve, reject) => {
				TL.Agent({
					agent: ["app", "get-user-reports"],
					data: {},
					success(Result) {
						TL.DebugLog("Fetched Reports:", Result);

						// Process the API data and update state
						State.ReportData = ProcessReportsData(Result);
						State.IsDataLoaded = true;

						// Render the dynamic category list
						RenderCategoryList();

						resolve(Result);
					},
					error(error) {
						TL.Notify.Banner("Error", error);
						reject(error);
					},
				});
			});
		}

		/*
		 **
		 ** Retrieve the requested report
		 ** =============================
		 */
		function RetrieveReport(ReportTitle) {
			// Start loading
			TL.Loading.Start("main");
			TL.Agent({
				agent: ["app", "retrieve"],
				data: {
					ReportName: ReportTitle,
				},
				success(Result) {
					TL.DebugLog("Report retrieved:", Result);
					TL.Loading.Stop("main");
				},

				//Agent returned an error, pass it to the user
				error(error) {
					TL.Notify.Banner("Error", error);
					TL.Loading.Stop("main");
				},
			});
		}
	});
});
